const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
    constructor(dbPath = './pumpfun_data.db') {
        this.dbPath = dbPath;
        this.db = null;
    }

    async init() {
        return new Promise((resolve, reject) => {
            this.db = new sqlite3.Database(this.dbPath, (err) => {
                if (err) {
                    console.error('Error opening database:', err);
                    reject(err);
                } else {
                    console.log('Connected to SQLite database:', this.dbPath);
                    this.createTables().then(resolve).catch(reject);
                }
            });
        });
    }

    async createTables() {
        const tables = [
            // Users table - central user registry
            `CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                user_address TEXT,
                followers TEXT,
                first_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,

            // Chat messages table
            `CREATE TABLE IF NOT EXISTS chat_messages (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                user_address TEXT,
                message TEXT NOT NULL,
                is_valid_move BOOLEAN DEFAULT FALSE,
                timestamp DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // Viewer sessions table - tracks when users are viewing
            `CREATE TABLE IF NOT EXISTS viewer_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                username TEXT NOT NULL,
                user_role TEXT NOT NULL CHECK (user_role IN ('host', 'moderator', 'viewer')),
                followers TEXT,
                is_you BOOLEAN DEFAULT FALSE,
                session_timestamp DATETIME NOT NULL,
                scrape_session_id TEXT NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )`,

            // Scrape sessions table - tracks each scraping run
            `CREATE TABLE IF NOT EXISTS scrape_sessions (
                id TEXT PRIMARY KEY,
                total_hosts INTEGER DEFAULT 0,
                total_moderators INTEGER DEFAULT 0,
                total_viewers INTEGER DEFAULT 0,
                timestamp DATETIME NOT NULL,
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
        ];

        for (const table of tables) {
            await this.runQuery(table);
        }

        // Create indexes for optimization
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_users_username ON users (username)',
            'CREATE INDEX IF NOT EXISTS idx_users_user_address ON users (user_address)',
            'CREATE INDEX IF NOT EXISTS idx_chat_messages_timestamp ON chat_messages (timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_chat_messages_is_valid_move ON chat_messages (is_valid_move)',
            'CREATE INDEX IF NOT EXISTS idx_viewer_sessions_timestamp ON viewer_sessions (session_timestamp)',
            'CREATE INDEX IF NOT EXISTS idx_viewer_sessions_user_id ON viewer_sessions (user_id)',
            'CREATE INDEX IF NOT EXISTS idx_viewer_sessions_scrape_session ON viewer_sessions (scrape_session_id)',
            'CREATE INDEX IF NOT EXISTS idx_scrape_sessions_timestamp ON scrape_sessions (timestamp)'
        ];

        for (const index of indexes) {
            await this.runQuery(index);
        }

        console.log('Database tables and indexes created successfully');
    }

    runQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.run(sql, params, function (err) {
                if (err) {
                    reject(err);
                } else {
                    resolve({ id: this.lastID, changes: this.changes });
                }
            });
        });
    }

    getQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.get(sql, params, (err, row) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(row);
                }
            });
        });
    }

    allQuery(sql, params = []) {
        return new Promise((resolve, reject) => {
            this.db.all(sql, params, (err, rows) => {
                if (err) {
                    reject(err);
                } else {
                    resolve(rows);
                }
            });
        });
    }

    // User management methods
    async upsertUser(username, userAddress = null, followers = null) {
        try {
            // First try to get existing user
            const existingUser = await this.getQuery(
                'SELECT id FROM users WHERE username = ? OR (user_address = ? AND user_address IS NOT NULL)',
                [username, userAddress]
            );

            if (existingUser) {
                // Update existing user
                await this.runQuery(
                    `UPDATE users SET 
                     username = ?, 
                     user_address = COALESCE(?, user_address),
                     followers = COALESCE(?, followers),
                     last_seen = CURRENT_TIMESTAMP,
                     updated_at = CURRENT_TIMESTAMP
                     WHERE id = ?`,
                    [username, userAddress, followers, existingUser.id]
                );
                return existingUser.id;
            } else {
                // Insert new user
                const result = await this.runQuery(
                    `INSERT INTO users (username, user_address, followers, first_seen, last_seen) 
                     VALUES (?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
                    [username, userAddress, followers]
                );
                return result.id;
            }
        } catch (error) {
            console.error('Error upserting user:', error);
            throw error;
        }
    }

    // Chat message methods
    async insertChatMessage(messageData) {
        try {
            const { username, userAddress, message, timestamp } = messageData;
            const validMoves = ['up', 'down', 'left', 'right', 'a', 'b', 'start', 'select'];
            const isValidMove = validMoves.includes(message.toLowerCase().trim());

            // Upsert user first
            const userId = await this.upsertUser(username, userAddress);

            // Insert chat message
            const result = await this.runQuery(
                `INSERT INTO chat_messages (user_id, username, user_address, message, is_valid_move, timestamp)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [userId, username, userAddress, message, isValidMove, new Date(timestamp).toISOString()]
            );

            console.log(`💬 Stored chat message from ${username}: "${message}" ${isValidMove ? '(VALID MOVE)' : ''}`);
            return result.id;
        } catch (error) {
            console.error('Error inserting chat message:', error);
            throw error;
        }
    }

    // Viewer session methods
    async insertViewerSession(sessionData) {
        try {
            const { participants, timestamp, sessionId } = sessionData;

            // Insert scrape session record
            await this.runQuery(
                `INSERT OR REPLACE INTO scrape_sessions (id, total_hosts, total_moderators, total_viewers, timestamp)
                 VALUES (?, ?, ?, ?, ?)`,
                [
                    sessionId,
                    participants.host.length,
                    participants.moderators.length,
                    participants.viewers.length,
                    new Date(timestamp).toISOString()
                ]
            );

            let totalInserted = 0;

            // Insert all participants
            for (const [role, users] of Object.entries(participants)) {
                for (const user of users) {
                    // Upsert user first
                    const userId = await this.upsertUser(user.username, null, user.followers);

                    // Insert viewer session record
                    await this.runQuery(
                        `INSERT INTO viewer_sessions (user_id, username, user_role, followers, is_you, session_timestamp, scrape_session_id)
                         VALUES (?, ?, ?, ?, ?, ?, ?)`,
                        [
                            userId,
                            user.username,
                            role === 'host' ? 'host' : role === 'moderators' ? 'moderator' : 'viewer',
                            user.followers,
                            user.isYou || false,
                            new Date(timestamp).toISOString(),
                            sessionId
                        ]
                    );
                    totalInserted++;
                }
            }

            console.log(`📊 Stored viewer session: ${participants.host.length} hosts, ${participants.moderators.length} moderators, ${participants.viewers.length} viewers (${totalInserted} total records)`);
            return sessionId;
        } catch (error) {
            console.error('Error inserting viewer session:', error);
            throw error;
        }
    }

    // Query methods for analytics
    async getChatStats(timeframe = '24 HOURS') {
        const sql = `
            SELECT 
                COUNT(*) as total_messages,
                COUNT(DISTINCT user_id) as unique_users,
                COUNT(CASE WHEN is_valid_move = 1 THEN 1 END) as valid_moves,
                COUNT(CASE WHEN is_valid_move = 0 THEN 1 END) as invalid_messages
            FROM chat_messages 
            WHERE timestamp >= datetime('now', '-${timeframe}')
        `;
        return await this.getQuery(sql);
    }

    async getTopChatters(limit = 10, timeframe = '24 HOURS') {
        const sql = `
            SELECT 
                u.username,
                COUNT(*) as message_count,
                COUNT(CASE WHEN cm.is_valid_move = 1 THEN 1 END) as valid_moves
            FROM chat_messages cm
            JOIN users u ON cm.user_id = u.id
            WHERE cm.timestamp >= datetime('now', '-${timeframe}')
            GROUP BY u.id, u.username
            ORDER BY message_count DESC
            LIMIT ?
        `;
        return await this.allQuery(sql, [limit]);
    }

    async getViewerStats(timeframe = '24 HOURS') {
        const sql = `
            SELECT
                COUNT(DISTINCT id) as total_sessions,
                AVG(total_viewers) as avg_viewers,
                MAX(total_viewers) as peak_viewers,
                MIN(total_viewers) as min_viewers
            FROM scrape_sessions
            WHERE timestamp >= datetime('now', '-${timeframe}')
        `;
        return await this.getQuery(sql);
    }

    // User address management methods
    async getUsersWithoutAddress(limit = 50) {
        try {
            const sql = `
                SELECT username
                FROM users
                WHERE user_address IS NULL
                ORDER BY last_seen DESC
                LIMIT ?
            `;
            return await this.allQuery(sql, [limit]);
        } catch (error) {
            console.error('Error getting users without address:', error);
            throw error;
        }
    }

    async updateUserAddress(username, userAddress) {
        try {
            const result = await this.runQuery(
                `UPDATE users SET
                 user_address = ?,
                 updated_at = CURRENT_TIMESTAMP
                 WHERE username = ?`,
                [userAddress, username]
            );

            if (result.changes > 0) {
                console.log(`✅ Updated address for ${username}: ${userAddress}`);
            }

            return result.changes > 0;
        } catch (error) {
            console.error('Error updating user address:', error);
            throw error;
        }
    }

    async close() {
        return new Promise((resolve) => {
            if (this.db) {
                this.db.close((err) => {
                    if (err) {
                        console.error('Error closing database:', err);
                    } else {
                        console.log('Database connection closed');
                    }
                    resolve();
                });
            } else {
                resolve();
            }
        });
    }
}

module.exports = Database;
