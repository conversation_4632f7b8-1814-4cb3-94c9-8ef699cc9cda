require('dotenv').config();
const PumpFunWebSocketClient = require('./websocket-client');
const Database = require('./database');

async function demonstrateDatabase() {
    console.log('🚀 Starting PumpFun Database Demo');
    
    // Initialize database
    const db = new Database();
    await db.init();
    
    // Example: Insert some test chat messages
    console.log('\n📝 Inserting test chat messages...');
    
    const testMessages = [
        { username: 'player1', userAddress: '0x123...abc', message: 'up', timestamp: Date.now() },
        { username: 'player2', userAddress: '0x456...def', message: 'hello world', timestamp: Date.now() + 1000 },
        { username: 'player1', userAddress: '0x123...abc', message: 'down', timestamp: Date.now() + 2000 },
        { username: 'player3', userAddress: null, message: 'left', timestamp: Date.now() + 3000 }
    ];
    
    for (const msg of testMessages) {
        await db.insertChatMessage(msg);
    }
    
    // Example: Insert test viewer session
    console.log('\n👥 Inserting test viewer session...');
    
    const testViewerSession = {
        participants: {
            host: [
                { username: 'streamhost', followers: '1.2k followers', isYou: false }
            ],
            moderators: [
                { username: 'mod1', followers: '500 followers', isYou: false }
            ],
            viewers: [
                { username: 'viewer1', followers: '100 followers', isYou: false },
                { username: 'viewer2', followers: '50 followers', isYou: false },
                { username: 'player1', followers: '200 followers', isYou: false }
            ]
        },
        timestamp: new Date().toISOString(),
        sessionId: `demo_${Date.now()}`
    };
    
    await db.insertViewerSession(testViewerSession);
    
    // Query and display statistics
    console.log('\n📊 Database Statistics:');
    
    const chatStats = await db.getChatStats();
    console.log('Chat Stats (24h):', chatStats);
    
    const topChatters = await db.getTopChatters(5);
    console.log('Top Chatters:', topChatters);
    
    const viewerStats = await db.getViewerStats();
    console.log('Viewer Stats (24h):', viewerStats);
    
    // Close database
    await db.close();
    console.log('\n✅ Database demo completed!');
}

async function startWebSocketWithDatabase() {
    console.log('\n🔌 Starting WebSocket client with database integration...');
    
    // Replace with your actual room ID
    const roomId = 'your-room-id-here';
    const client = new PumpFunWebSocketClient(roomId, true); // Enable database
    
    // Initialize database
    await client.initDatabase();
    
    // Set up event listeners
    client.on('validMove', (data) => {
        console.log(`🎮 Valid move received: ${data.message} from ${data.username}`);
    });
    
    client.on('chatMessage', (data) => {
        console.log(`💬 Chat message: ${data.username}: ${data.message}`);
    });
    
    try {
        await client.connect();
        console.log('✅ WebSocket connected and database ready!');
        
        // Keep running for demo purposes
        console.log('🔄 Client is running... Press Ctrl+C to stop');
        
        // Graceful shutdown
        process.on('SIGINT', async () => {
            console.log('\n🛑 Shutting down...');
            await client.disconnect();
            process.exit(0);
        });
        
    } catch (error) {
        console.error('❌ Error:', error);
        await client.disconnect();
    }
}

// Main execution
async function main() {
    const args = process.argv.slice(2);
    
    if (args.includes('--demo-db')) {
        await demonstrateDatabase();
    } else if (args.includes('--websocket')) {
        await startWebSocketWithDatabase();
    } else {
        console.log('🎯 PumpFun Database Integration Examples');
        console.log('');
        console.log('Usage:');
        console.log('  node example-with-database.js --demo-db     # Demonstrate database operations');
        console.log('  node example-with-database.js --websocket  # Start WebSocket with database');
        console.log('');
        console.log('📋 Database Features:');
        console.log('  ✅ Users table with automatic user management');
        console.log('  ✅ Chat messages with valid move detection');
        console.log('  ✅ Viewer sessions with role tracking');
        console.log('  ✅ Optimized indexes for fast queries');
        console.log('  ✅ Analytics and statistics methods');
        console.log('');
        console.log('🗃️ Database file: ./pumpfun_data.db');
        console.log('');
        
        // Show a quick demo
        await demonstrateDatabase();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = {
    demonstrateDatabase,
    startWebSocketWithDatabase
};
