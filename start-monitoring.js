require('dotenv').config();
const PumpFunWebSocketClient = require('./websocket-client');
const { PumpFunViewerScraper } = require('./pumpfun-viewer-scraper');
const Database = require('./database');

class PumpFunMonitor {
    constructor(roomId, url, options = {}) {
        this.roomId = roomId;
        this.url = url;
        this.options = {
            enableDatabase: true,
            viewerScrapeInterval: 5, // minutes
            enableWebSocket: true,
            enableViewerScraper: true,
            enableUserAddressFetching: true, // Now handled by viewer scraper
            ...options
        };

        this.database = null;
        this.webSocketClient = null;
        this.viewerScraper = null;
        this.isRunning = false;
    }

    async init() {
        console.log('🚀 Initializing PumpFun Monitor...');

        // Initialize database
        if (this.options.enableDatabase) {
            this.database = new Database();
            await this.database.init();
            console.log('✅ Database initialized');
        }

        // Initialize WebSocket client
        if (this.options.enableWebSocket) {
            this.webSocketClient = new PumpFunWebSocketClient(this.roomId, this.options.enableDatabase);
            if (this.options.enableDatabase) {
                await this.webSocketClient.initDatabase();
            }
            console.log('✅ WebSocket client initialized');
        }

        // Initialize viewer scraper
        if (this.options.enableViewerScraper) {
            this.viewerScraper = new PumpFunViewerScraper(this.options.enableDatabase);
            await this.viewerScraper.init();
            if (this.options.enableDatabase) {
                await this.viewerScraper.initDatabase();
            }
            console.log('✅ Viewer scraper initialized');
        }


    }

    async start() {
        if (this.isRunning) {
            console.log('⚠️  Monitor is already running');
            return;
        }

        this.isRunning = true;
        console.log('\n🎯 Starting PumpFun Monitor...');
        console.log(`📍 Room ID: ${this.roomId}`);
        console.log(`🌐 URL: ${this.url}`);
        console.log(`🗃️  Database: ${this.options.enableDatabase ? 'Enabled' : 'Disabled'}`);
        console.log(`💬 WebSocket: ${this.options.enableWebSocket ? 'Enabled' : 'Disabled'}`);
        console.log(`👥 Viewer Scraper: ${this.options.enableViewerScraper ? 'Enabled' : 'Disabled'}`);
        console.log(`🔍 User Address Fetching: ${this.options.enableUserAddressFetching ? 'Enabled' : 'Disabled'}`);
        console.log(`⏱️  Scrape Interval: ${this.options.viewerScrapeInterval} minutes\n`);

        // Start WebSocket client
        if (this.webSocketClient) {
            try {
                await this.webSocketClient.connect();
                console.log('✅ WebSocket connected - monitoring chat messages');

                this.webSocketClient.on('validMove', (data) => {
                    console.log(`🎮 Valid move: ${data.message} from ${data.username}`);
                });

                this.webSocketClient.on('chatMessage', (data) => {
                    console.log(`💬 Chat: ${data.username}: ${data.message}`);
                });

                // Start connection health monitoring
                this.startWebSocketHealthCheck();
            } catch (error) {
                console.error('❌ Failed to connect WebSocket:', error.message);
                console.log('🔄 WebSocket will attempt to reconnect automatically...');
            }
        }

        // Start viewer scraper
        if (this.viewerScraper) {
            try {
                console.log('🔄 Starting viewer monitoring...');
                // Don't await this - it runs continuously
                this.startViewerMonitoring();
            } catch (error) {
                console.error('❌ Failed to start viewer scraper:', error.message);
            }
        }



        // Set up graceful shutdown
        process.on('SIGINT', () => {
            console.log('\n🛑 Shutting down monitor...');
            this.stop();
        });

        console.log('✅ Monitor is running! Press Ctrl+C to stop\n');
    }

    async startViewerMonitoring() {
        // Navigate to the page once with proper initialization
        await this.viewerScraper.handleInitialPageLoad(this.url);
        await this.viewerScraper.setupInitialState();

        // Start user address fetching if enabled
        if (this.options.enableUserAddressFetching) {
            this.viewerScraper.startUserAddressFetching();
        }

        let cycleCount = 0;
        const intervalMs = this.options.viewerScrapeInterval * 60 * 1000;

        const monitoringInterval = setInterval(async () => {
            try {
                cycleCount++;
                const timestamp = new Date().toLocaleString();
                console.log(`\n🕐 [${timestamp}] Viewer scrape cycle #${cycleCount}`);

                const result = await this.viewerScraper.harvestViewerData();

                if (result.success) {
                    const { totalViewers, totalModerators, totalHosts } = result.data;

                    // Check if we got 0 results - might need to refresh
                    if (totalViewers === 0 && totalModerators === 0 && totalHosts === 0) {
                        console.log('⚠️  Got 0 viewers/moderators/hosts - refreshing page...');
                        try {
                            await this.viewerScraper.handleInitialPageLoad(this.url);
                            await this.viewerScraper.setupInitialState();
                            console.log('🔄 Page refreshed, will try again in next cycle');
                        } catch (refreshError) {
                            console.error('❌ Failed to refresh page:', refreshError.message);
                        }
                    } else {
                        console.log(`📊 Found: ${totalHosts} hosts, ${totalModerators} moderators, ${totalViewers} viewers`);

                        // Show some viewer names
                        if (result.data.participants.viewers.length > 0) {
                            const viewerNames = result.data.participants.viewers
                                .slice(0, 5)
                                .map(v => v.username)
                                .join(', ');
                            console.log(`👥 Viewers: ${viewerNames}${result.data.participants.viewers.length > 5 ? '...' : ''}`);
                        }
                    }
                } else {
                    console.error(`❌ Scrape failed: ${result.error}`);
                }

                console.log(`⏰ Next scrape in ${this.options.viewerScrapeInterval} minutes`);
            } catch (error) {
                console.error(`💥 Error during viewer monitoring: ${error.message}`);
            }
        }, intervalMs);

        // Store interval for cleanup
        this.viewerInterval = monitoringInterval;
    }



    startWebSocketHealthCheck() {
        // Check WebSocket connection every 30 seconds
        this.wsHealthInterval = setInterval(() => {
            if (this.webSocketClient && !this.webSocketClient.isWebSocketConnected()) {
                console.log('⚠️  WebSocket connection lost, attempting reconnection...');
                this.webSocketClient.reconnect().catch(error => {
                    console.error('❌ Health check reconnection failed:', error.message);
                });
            }
        }, 30000);
    }

    async stop() {
        this.isRunning = false;

        console.log('🔄 Stopping components...');

        // Stop viewer scraper interval
        if (this.viewerInterval) {
            clearInterval(this.viewerInterval);
            this.viewerInterval = null;
        }



        // Stop WebSocket health check
        if (this.wsHealthInterval) {
            clearInterval(this.wsHealthInterval);
            this.wsHealthInterval = null;
        }

        // Close WebSocket
        if (this.webSocketClient) {
            await this.webSocketClient.disconnect();
            console.log('✅ WebSocket disconnected');
        }

        // Close viewer scraper
        if (this.viewerScraper) {
            await this.viewerScraper.close();
            console.log('✅ Viewer scraper closed');
        }



        // Close database
        if (this.database) {
            await this.database.close();
            console.log('✅ Database closed');
        }

        console.log('🏁 Monitor stopped successfully');
        process.exit(0);
    }

    async getStats() {
        if (!this.database) {
            console.log('❌ Database not enabled');
            return;
        }

        console.log('\n📊 Current Statistics:');

        const chatStats = await this.database.getChatStats('1 HOUR');
        console.log(`💬 Last hour: ${chatStats.total_messages} messages, ${chatStats.valid_moves} valid moves`);

        const viewerStats = await this.database.getViewerStats('1 HOUR');
        console.log(`👥 Last hour: ${viewerStats.total_sessions} sessions, peak ${viewerStats.peak_viewers} viewers`);
    }
}

// CLI interface
async function main() {
    const args = process.argv.slice(2);

    // Configuration
    const roomId = process.env.PUMP_ROOM_ID || 'EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump';
    const url = process.env.PUMP_URL || 'https://pump.fun/coin/EpxanDRMd9iDEYdozB2CBV6tuS5mnYqBLuXrRbNcpump';

    const options = {
        enableDatabase: !args.includes('--no-db'),
        enableWebSocket: !args.includes('--no-websocket'),
        enableViewerScraper: !args.includes('--no-scraper'),
        viewerScrapeInterval: args.includes('--interval') ?
            parseInt(args[args.indexOf('--interval') + 1]) || 5 : 5
    };

    if (args.includes('--help')) {
        console.log('🎯 PumpFun Monitor - Complete monitoring solution');
        console.log('');
        console.log('Usage: node start-monitoring.js [options]');
        console.log('');
        console.log('Options:');
        console.log('  --no-db          Disable database storage');
        console.log('  --no-websocket   Disable WebSocket chat monitoring');
        console.log('  --no-scraper     Disable viewer scraping');
        console.log('  --interval N     Set scrape interval in minutes (default: 5)');
        console.log('  --help           Show this help');
        console.log('');
        console.log('Environment variables:');
        console.log('  PUMP_ROOM_ID     WebSocket room ID');
        console.log('  PUMP_URL         PumpFun stream URL');
        console.log('  PUMP_WS          WebSocket endpoint');
        return;
    }

    const monitor = new PumpFunMonitor(roomId, url, options);

    try {
        await monitor.init();
        await monitor.start();

        // Keep the process alive
        return new Promise(() => { }); // Never resolves
    } catch (error) {
        console.error('💥 Fatal error:', error);
        await monitor.stop();
    }
}

if (require.main === module) {
    main().catch(console.error);
}

module.exports = PumpFunMonitor;
