require('dotenv').config();
const { PumpFunViewerScraper } = require('./pumpfun-viewer-scraper');

async function testIntegratedUserAddressFetcher() {
    console.log('🧪 Testing Integrated User Address Fetcher');

    const scraper = new PumpFunViewerScraper(true);

    try {
        await scraper.init();
        await scraper.initDatabase();

        // Test fetching a specific user
        console.log('\n🔍 Testing individual user fetch...');
        const testResult = await scraper.fetchUserAddress('pumpplaysgames');
        console.log('Test result:', testResult);

        // Test fetching missing addresses from database
        console.log('\n📋 Testing database fetch...');
        await scraper.fetchMissingUserAddresses();

        // Show queue status
        console.log('\n📊 Queue Status:', {
            queueLength: scraper.userAddressQueue.length,
            isProcessing: scraper.isProcessingUserAddresses
        });

        // Wait a bit to see processing
        console.log('\n⏳ Waiting 10 seconds to observe processing...');
        await new Promise(resolve => setTimeout(resolve, 10000));

        console.log('📊 Final Queue Status:', {
            queueLength: scraper.userAddressQueue.length,
            isProcessing: scraper.isProcessingUserAddresses
        });

    } catch (error) {
        console.error('❌ Test failed:', error);
    } finally {
        await scraper.close();
        console.log('✅ Test completed');
    }
}

// Run if called directly
if (require.main === module) {
    testIntegratedUserAddressFetcher().catch(console.error);
}

module.exports = { testIntegratedUserAddressFetcher };
