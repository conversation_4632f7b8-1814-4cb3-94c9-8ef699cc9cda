require('dotenv').config();
const https = require('https');
const Database = require('./database');

class UserAddressFetcher {
    constructor(enableDatabase = true) {
        this.enableDatabase = enableDatabase;
        this.database = null;
        this.isRunning = false;
        this.requestQueue = [];
        this.isProcessingQueue = false;
        this.rateLimitDelay = 500; // 500ms = 2 requests per second max
        this.lastRequestTime = 0;
        
        // Browser-like headers to avoid detection
        this.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'en-US,en;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Referer': 'https://pump.fun/',
            'Origin': 'https://pump.fun',
            'Connection': 'keep-alive',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-site',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache'
        };
    }

    async initDatabase() {
        if (this.enableDatabase) {
            try {
                this.database = new Database();
                await this.database.init();
                console.log('✅ Database initialized for User Address Fetcher');
            } catch (error) {
                console.error('❌ Failed to initialize database:', error);
                this.enableDatabase = false;
            }
        }
    }

    async fetchUserAddress(username) {
        return new Promise((resolve, reject) => {
            const url = `https://frontend-api-v3.pump.fun/users/${encodeURIComponent(username)}`;
            
            const options = {
                hostname: 'frontend-api-v3.pump.fun',
                port: 443,
                path: `/users/${encodeURIComponent(username)}`,
                method: 'GET',
                headers: this.headers,
                timeout: 10000 // 10 second timeout
            };

            const req = https.request(options, (res) => {
                let data = '';

                res.on('data', (chunk) => {
                    data += chunk;
                });

                res.on('end', () => {
                    try {
                        if (res.statusCode === 200) {
                            const userData = JSON.parse(data);
                            resolve({
                                success: true,
                                address: userData.address || null,
                                userData: userData
                            });
                        } else if (res.statusCode === 404) {
                            // User not found
                            resolve({
                                success: true,
                                address: 'none',
                                userData: null
                            });
                        } else {
                            resolve({
                                success: false,
                                error: `HTTP ${res.statusCode}: ${data}`,
                                address: null
                            });
                        }
                    } catch (parseError) {
                        resolve({
                            success: false,
                            error: `Parse error: ${parseError.message}`,
                            address: null
                        });
                    }
                });
            });

            req.on('error', (error) => {
                resolve({
                    success: false,
                    error: `Request error: ${error.message}`,
                    address: null
                });
            });

            req.on('timeout', () => {
                req.destroy();
                resolve({
                    success: false,
                    error: 'Request timeout',
                    address: null
                });
            });

            req.end();
        });
    }

    async enforceRateLimit() {
        const now = Date.now();
        const timeSinceLastRequest = now - this.lastRequestTime;
        
        if (timeSinceLastRequest < this.rateLimitDelay) {
            const waitTime = this.rateLimitDelay - timeSinceLastRequest;
            await new Promise(resolve => setTimeout(resolve, waitTime));
        }
        
        this.lastRequestTime = Date.now();
    }

    async processUserQueue() {
        if (this.isProcessingQueue || this.requestQueue.length === 0) {
            return;
        }

        this.isProcessingQueue = true;
        console.log(`🔄 Processing ${this.requestQueue.length} users in queue...`);

        while (this.requestQueue.length > 0 && this.isRunning) {
            const username = this.requestQueue.shift();
            
            try {
                await this.enforceRateLimit();
                
                console.log(`🔍 Fetching address for user: ${username}`);
                const result = await this.fetchUserAddress(username);
                
                if (result.success && this.enableDatabase && this.database) {
                    await this.database.updateUserAddress(username, result.address);
                    
                    if (result.address && result.address !== 'none') {
                        console.log(`✅ Found address for ${username}: ${result.address}`);
                    } else {
                        console.log(`ℹ️  No address found for ${username}, marked as 'none'`);
                    }
                } else if (!result.success) {
                    console.error(`❌ Failed to fetch address for ${username}: ${result.error}`);
                }
                
            } catch (error) {
                console.error(`❌ Error processing user ${username}:`, error);
            }
        }

        this.isProcessingQueue = false;
        console.log('✅ Queue processing completed');
    }

    async fetchMissingAddresses() {
        if (!this.enableDatabase || !this.database) {
            console.error('❌ Database not available for fetching missing addresses');
            return;
        }

        try {
            const usersWithoutAddress = await this.database.getUsersWithoutAddress(50);
            
            if (usersWithoutAddress.length === 0) {
                console.log('ℹ️  No users without addresses found');
                return;
            }

            console.log(`📋 Found ${usersWithoutAddress.length} users without addresses`);
            
            // Add users to queue
            for (const user of usersWithoutAddress) {
                if (!this.requestQueue.includes(user.username)) {
                    this.requestQueue.push(user.username);
                }
            }

            // Process the queue
            await this.processUserQueue();
            
        } catch (error) {
            console.error('❌ Error fetching missing addresses:', error);
        }
    }

    async start() {
        this.isRunning = true;
        console.log('🚀 User Address Fetcher started');
        
        if (this.enableDatabase) {
            await this.initDatabase();
        }
        
        // Initial fetch
        await this.fetchMissingAddresses();
    }

    async stop() {
        this.isRunning = false;
        this.requestQueue = [];
        console.log('🛑 User Address Fetcher stopped');
        
        if (this.database) {
            await this.database.close();
        }
    }

    // Method to manually add users to fetch queue
    addUserToQueue(username) {
        if (!this.requestQueue.includes(username)) {
            this.requestQueue.push(username);
            console.log(`➕ Added ${username} to fetch queue`);
        }
    }

    // Get queue status
    getQueueStatus() {
        return {
            queueLength: this.requestQueue.length,
            isProcessing: this.isProcessingQueue,
            isRunning: this.isRunning
        };
    }
}

module.exports = UserAddressFetcher;
