require('dotenv').config();
const WebSocket = require('ws');
const EventEmitter = require('events');
const Database = require('./database');

class PumpFunWebSocketClient extends EventEmitter {
    constructor(roomId, enableDatabase = true) {
        super();
        this.roomId = roomId;
        this.socket = null;
        this.participants = new Set();
        this.chatMessages = [];
        this.validMoves = ['up', 'down', 'left', 'right', 'a', 'b', 'start', 'select'];
        this.enableDatabase = enableDatabase;
        this.database = null;
        this.isConnected = false;
        this.shouldReconnect = true;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 10;
        this.reconnectDelay = 5000; // Start with 5 seconds
        this.maxReconnectDelay = 60000; // Max 1 minute
        this.reconnectTimer = null;
    }

    async initDatabase() {
        if (this.enableDatabase) {
            try {
                this.database = new Database();
                await this.database.init();
                console.log('✅ Database initialized for WebSocket client');
            } catch (error) {
                console.error('❌ Failed to initialize database:', error);
                this.enableDatabase = false;
            }
        }
    }

    connect() {
        return new Promise((resolve, reject) => {
            if (this.socket && this.isConnected) {
                console.log('Already connected to WebSocket');
                resolve();
                return;
            }

            console.log('🔌 Connecting to PumpFun websocket...');
            this.socket = new WebSocket(process.env.PUMP_WS, {
                headers: {
                    'Origin': 'https://pump.fun',
                    'User-Agent': 'Mozilla/5.0'
                }
            });

            this.socket.on('open', () => {
                console.log('✅ Connected to PumpFun websocket');
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.reconnectDelay = 5000; // Reset delay

                if (this.reconnectTimer) {
                    clearTimeout(this.reconnectTimer);
                    this.reconnectTimer = null;
                }

                resolve();
            });

            this.socket.on('error', (error) => {
                console.error('❌ WebSocket connection error:', error.message);
                this.isConnected = false;

                if (this.reconnectAttempts === 0) {
                    // Only reject on first connection attempt
                    reject(error);
                }
            });

            this.socket.on('close', (code, reason) => {
                console.log(`🔌 WebSocket disconnected (code: ${code}, reason: ${reason})`);
                this.isConnected = false;
                this.socket = null;

                if (this.shouldReconnect) {
                    this.scheduleReconnect();
                }
            });

            this.socket.on('message', (data) => {
                this.handleMessage(data.toString());
            });
        });
    }

    scheduleReconnect() {
        if (!this.shouldReconnect || this.reconnectTimer) {
            return;
        }

        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error(`❌ Max reconnection attempts (${this.maxReconnectAttempts}) reached. Stopping reconnection.`);
            return;
        }

        this.reconnectAttempts++;
        const delay = Math.min(this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);

        console.log(`🔄 Scheduling reconnection attempt ${this.reconnectAttempts}/${this.maxReconnectAttempts} in ${delay / 1000} seconds...`);

        this.reconnectTimer = setTimeout(async () => {
            this.reconnectTimer = null;

            try {
                await this.connect();
                console.log('✅ Successfully reconnected to WebSocket');
            } catch (error) {
                console.error('❌ Reconnection failed:', error.message);
                this.scheduleReconnect();
            }
        }, delay);
    }

    handleMessage(message) {
        // Handle ping/pong
        if (message === '2') {
            this.socket.send('3');
            return;
        }

        // Initial handshake
        if (message.startsWith('0')) {
            const connectPayload = {
                origin: 'https://pump.fun',
                timestamp: Date.now(),
                token: null
            };
            this.socket.send('40' + JSON.stringify(connectPayload));
            return;
        }

        // Join room after handshake
        if (message.startsWith('40')) {
            this.joinRoom();
            return;
        }

        // Process Socket.IO messages
        if (message.startsWith('42')) {
            try {
                const data = JSON.parse(message.substring(2));
                this.processSocketMessage(data);
            } catch (error) {
                console.error('Parse error:', error);
            }
        }
    }

    joinRoom() {
        if (this.socket && this.roomId) {
            const joinPayload = [
                'joinRoom',
                { roomId: this.roomId, username: '' }
            ];
            this.socket.send('42' + JSON.stringify(joinPayload));
            console.log(`Joined room: ${this.roomId}`);
        }
    }

    processSocketMessage(data) {
        if (!Array.isArray(data) || data.length < 1) {
            return;
        };

        const [eventType, eventData] = data;

        switch (eventType) {
            case 'userJoined':
                // We do not care for this data
                break;

            case 'userLeft':
                // We do not care for this data
                break;

            case 'newMessage':
            case 'message':
                // Only store valid moves in chatMessages array
                const message = eventData.message?.toLowerCase().trim();
                if (message && this.validMoves.includes(message)) {
                    this.chatMessages.push({
                        ...eventData,
                        timestamp: Date.now()
                    });
                }
                this.handleChatMessage(eventData);
                break;

            case 'setCookie':
                // Ignore cookie events
                break;

            default:
                // Silently ignore unknown events
                break;
        }
    }

    async handleChatMessage(data) {
        const message = data.message?.toLowerCase().trim();
        const userId = data.userAddress || data.userId || data.username;

        // Check if this is a valid move
        const isValidMove = userId && message && this.validMoves.includes(message);

        if (isValidMove) {
            // Print when receiving valid move
            console.log(`🎮 Valid move received: "${message}" from player: ${data.username || userId.substring(0, 8)}`);
        }

        // Only store valid moves in database if enabled
        if (this.enableDatabase && this.database && data.username && data.message && isValidMove) {
            try {
                await this.database.insertChatMessage({
                    username: data.username,
                    userAddress: data.userAddress || data.userId,
                    message: data.message,
                    timestamp: Date.now()
                });

                // Print when storing valid move to database
                console.log(`💾 Valid move stored: "${message}" from ${data.username}`);
            } catch (error) {
                console.error('Error storing chat message in database:', error);
            }
        }

        if (isValidMove) {
            this.emit('validMove', {
                userId,
                username: data.username,
                message,
                timestamp: Date.now()
            });
        }

        // Emit all chat messages for potential other uses
        this.emit('chatMessage', data);
    }

    getParticipants() {
        return Array.from(this.participants);
    }

    getChatMessages() {
        return this.chatMessages;
    }

    async disconnect() {
        console.log('🔌 Disconnecting WebSocket...');
        this.shouldReconnect = false;
        this.isConnected = false;

        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = null;
        }

        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }

        if (this.database) {
            await this.database.close();
            this.database = null;
        }

        console.log('✅ WebSocket disconnected');
    }

    // Method to check connection status
    isWebSocketConnected() {
        return this.isConnected && this.socket && this.socket.readyState === WebSocket.OPEN;
    }

    // Method to manually trigger reconnection
    async reconnect() {
        console.log('🔄 Manual reconnection triggered...');
        this.shouldReconnect = true;
        this.reconnectAttempts = 0;

        if (this.socket) {
            this.socket.close();
        }

        try {
            await this.connect();
            return true;
        } catch (error) {
            console.error('❌ Manual reconnection failed:', error.message);
            return false;
        }
    }
}

module.exports = PumpFunWebSocketClient;